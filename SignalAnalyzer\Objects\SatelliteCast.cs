﻿using GeoEngine.API;
using SignalAnalyzer.Utilities;
using System;

namespace SignalAnalyzer.Objects
{
    /// <summary>
    /// 预报附加信息
    /// </summary>
    internal class SatelliteCast : OrbitDataView
    {
        public long InTimeMills
        {
            get { return LocalTimeChange.DateTimeToLong(InTime); }
        }
        public string InTimePrint
        {
            get { return InTime.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/"); }
        }
        
        public long OutTimeMills
        {
            get { return LocalTimeChange.DateTimeToLong(OutTime); }
        }
        public string OutTimePrint
        {
            get { return OutTime.ToString("yyyy/MM/dd HH:mm:ss").Replace("-", "/"); }
        }

        // 卫星观察姿态
        public string ObservePosture
        {
            get
            {
                switch (base.OrbitTrend)
                {
                    case OrbitTrendType.eLeftUp:
                        return "上行左视";
                    case OrbitTrendType.eRightDown:
                        return "下行右视";
                    case OrbitTrendType.eRightUp:
                        return "上行右视";
                    case OrbitTrendType.eLeftDown:
                        return "下行左视";
                    default:
                        return "未知";
                }
            }
        }
        
        // 卫星最大俯仰角是否大于10°
        public bool ElvThenTen
        {
            set { }
            get
            {
                return OverElevationDeg > 10;
            }
        }

    }
}
